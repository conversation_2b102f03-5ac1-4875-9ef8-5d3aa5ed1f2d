import React from 'react';
import noMiniappImage from '~/assets/imgs/no-miniapp.png';

interface MiniappEmptyStateProps {
  onNewProject: () => void;
  onClose?: () => void;
}

const MiniappEmptyState: React.FC<MiniappEmptyStateProps> = ({ onNewProject, onClose }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        backgroundColor: '#ffffff',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      {/* Main Content */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          flex: 1,
          padding: '40px 20px',
          gap: '32px',
        }}
      >
        {/* Illustration */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '16px',
          }}
        >
          <img
            src={noMiniappImage}
            alt="No MiniApp illustration"
            style={{
              width: '200px',
              height: 'auto',
              opacity: 0.8,
            }}
          />
        </div>

        {/* Text Content */}
        <div
          style={{
            textAlign: 'center',
            maxWidth: '300px',
          }}
        >
          <h2
            style={{
              fontSize: '24px',
              fontWeight: 600,
              color: '#111827',
              margin: '0 0 8px 0',
              lineHeight: 1.3,
            }}
          >
            Sorry,
          </h2>
          <p
            style={{
              fontSize: '24px',
              fontWeight: 600,
              color: '#111827',
              margin: 0,
              lineHeight: 1.3,
            }}
          >
            you currently do not have a MiniApp.
          </p>
        </div>

        {/* New Project Button */}
        <button
          onClick={onNewProject}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
            padding: '14px 32px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            backgroundColor: '#ffffff',
            color: '#374151',
            fontSize: '16px',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minWidth: '180px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#f9fafb';
            e.currentTarget.style.borderColor = '#9ca3af';
            e.currentTarget.style.boxShadow = '0 2px 4px 0 rgba(0, 0, 0, 0.1)';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = '#ffffff';
            e.currentTarget.style.borderColor = '#d1d5db';
            e.currentTarget.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
          }}
        >
          <span
            style={{
              fontSize: '18px',
              fontWeight: 600,
              color: '#374151',
            }}
          >
            +
          </span>
          New Project
        </button>
      </div>
    </div>
  );
};

export default MiniappEmptyState;
